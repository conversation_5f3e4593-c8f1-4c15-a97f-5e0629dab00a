import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
  Res,
  BadRequestException,
  Body,
  Put,
  Query,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { MediaService } from './media.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Media } from './media.entity';

@ApiTags('media')
@Controller('api/media')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class MediaController {
  constructor(private readonly mediaService: MediaService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Upload a media file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Media file to upload',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Media uploaded successfully',
    type: Media,
  })
  @ApiResponse({ status: 400, description: 'Invalid file or file too large' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadMedia(
    @UploadedFile() file: Express.Multer.File,
    @Request() req: any,
  ): Promise<Media> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    const username = req.user.username;
    return this.mediaService.uploadMedia(file, username);
  }

  @Get('user/my-media')
  @ApiOperation({ summary: 'Get all media uploaded by the current user' })
  @ApiResponse({
    status: 200,
    description: 'User media retrieved successfully',
    type: [Media],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getMyMedia(@Request() req: any): Promise<Media[]> {
    const username = req.user.username;
    return this.mediaService.getMediaByUser(username);
  }

  @Get(':id/signed-url')
  @ApiOperation({ summary: 'Get signed URL for media file' })
  @ApiResponse({
    status: 200,
    description: 'Signed URL generated successfully',
    schema: {
      type: 'object',
      properties: {
        media: { $ref: '#/components/schemas/Media' },
        signedUrl: { type: 'string' },
        expiresAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getMediaSignedUrl(
    @Param('id') id: string,
    @Query('expiresIn') expiresIn?: string,
  ): Promise<{
    media: Media;
    signedUrl: string;
    expiresAt: Date;
  }> {
    const expiresInSeconds = expiresIn ? parseInt(expiresIn, 10) : 3600; // Default 1 hour
    return this.mediaService.getMediaSignedUrl(id, expiresInSeconds);
  }

  @Post('upload-url')
  @ApiOperation({ summary: 'Generate signed URL for direct upload to S3' })
  @ApiBody({
    description: 'Upload request details',
    schema: {
      type: 'object',
      properties: {
        filename: { type: 'string' },
        contentType: { type: 'string' },
        expiresIn: { type: 'number' },
      },
      required: ['filename', 'contentType'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Upload URL generated successfully',
    schema: {
      type: 'object',
      properties: {
        uploadUrl: { type: 'string' },
        mediaId: { type: 'string' },
        s3Key: { type: 'string' },
        expiresAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async generateUploadUrl(
    @Request() req: any,
    @Body() body: {
      filename: string;
      contentType: string;
      expiresIn?: number;
    },
  ): Promise<{
    uploadUrl: string;
    mediaId: string;
    s3Key: string;
    expiresAt: Date;
  }> {
    const username = req.user.username;
    return this.mediaService.generateUploadSignedUrl(
      body.filename,
      body.contentType,
      username,
      body.expiresIn || 3600,
    );
  }

  @Put(':id/complete-upload')
  @ApiOperation({ summary: 'Complete media upload and update metadata' })
  @ApiBody({
    description: 'Upload completion details',
    schema: {
      type: 'object',
      properties: {
        fileSize: { type: 'number' },
        width: { type: 'number' },
        height: { type: 'number' },
        duration: { type: 'number' },
      },
      required: ['fileSize'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Media upload completed successfully',
    type: Media,
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async completeUpload(
    @Param('id') id: string,
    @Body() body: {
      fileSize: number;
      width?: number;
      height?: number;
      duration?: number;
    },
  ): Promise<Media> {
    return this.mediaService.updateMediaAfterUpload(id, body.fileSize, {
      width: body.width,
      height: body.height,
      duration: body.duration,
    });
  }

  // Replace the old file streaming endpoint with signed URL response
  @Get(':id')
  @ApiOperation({ summary: 'Get signed URL for media file (RECOMMENDED)' })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Media file ID',
  })
  @ApiQuery({
    name: 'expiresIn',
    type: 'number',
    required: false,
    description: 'Signed URL expiration in seconds (default: 3600)',
  })
  @ApiResponse({
    status: 200,
    description: 'Signed URL generated successfully',
    schema: {
      type: 'object',
      properties: {
        media: { $ref: '#/components/schemas/Media' },
        signedUrl: { type: 'string' },
        expiresAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getMediaSignedUrlDefault(
    @Param('id') id: string,
    @Query('expiresIn') expiresIn?: string,
  ): Promise<{
    media: Media;
    signedUrl: string;
    expiresAt: Date;
  }> {
    const expiresInSeconds = expiresIn ? parseInt(expiresIn, 10) : 3600; // Default 1 hour
    return this.mediaService.getMediaSignedUrl(id, expiresInSeconds);
  }

  @Get(':id/thumbnail')
  @ApiOperation({ summary: 'Get media thumbnail' })
  @ApiResponse({
    status: 200,
    description: 'Thumbnail retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Thumbnail not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getThumbnail(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const { media, buffer } = await this.mediaService.getThumbnail(id);
    
    res.set({
      'Content-Type': 'image/jpeg',
      'Content-Length': buffer.length.toString(),
      'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
    });
    
    res.send(buffer);
  }

  @Get(':id/info')
  @ApiOperation({ summary: 'Get media information' })
  @ApiResponse({
    status: 200,
    description: 'Media information retrieved successfully',
    type: Media,
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getMediaInfo(@Param('id') id: string): Promise<Media> {
    return this.mediaService.getMedia(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a media file' })
  @ApiResponse({
    status: 200,
    description: 'Media deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async deleteMedia(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<{ message: string }> {
    // TODO: Add authorization check - only allow deletion by uploader or admin
    await this.mediaService.deleteMedia(id);
    return { message: 'Media deleted successfully' };
  }
}
