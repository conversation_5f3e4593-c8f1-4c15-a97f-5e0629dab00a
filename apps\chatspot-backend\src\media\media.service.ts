import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Media, MediaType } from './media.entity';
import { S3ConfigService } from './s3-config.service';
import { GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import * as fs from 'fs';
import * as path from 'path';
import * as sharp from 'sharp';

@Injectable()
export class MediaService {
  private readonly uploadDir = 'uploads/media';
  private readonly thumbnailDir = 'uploads/thumbnails';
  private readonly maxFileSize = 10 * 1024 * 1024; // 10MB

  constructor(
    @InjectRepository(Media)
    private mediaRepository: Repository<Media>,
    private s3ConfigService: S3ConfigService,
  ) {
    // Ensure upload directories exist only if not using S3
    if (!this.s3ConfigService.isS3Enabled()) {
      this.ensureDirectoryExists(this.uploadDir);
      this.ensureDirectoryExists(this.thumbnailDir);
    }
  }

  private ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }

  private getMediaType(mimeType: string): MediaType {
    if (mimeType.startsWith('image/')) return 'image';
    throw new Error('Unsupported media type. Only images are supported.');
  }

  private isValidMimeType(mimeType: string): boolean {
    const allowedTypes = [
      // Images only
      'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif',
    ];
    return allowedTypes.includes(mimeType);
  }

  async uploadMedia(
    file: Express.Multer.File,
    uploadedBy: string,
  ): Promise<Media> {
    // Validate file size
    if (file.size > this.maxFileSize) {
      throw new BadRequestException(
        `File size exceeds maximum limit of ${this.maxFileSize / (1024 * 1024)}MB`,
      );
    }

    // Validate MIME type
    if (!this.isValidMimeType(file.mimetype)) {
      throw new BadRequestException('Unsupported file type');
    }

    const mediaType = this.getMediaType(file.mimetype);
    let filePath: string;
    let filename: string;
    let s3Key: string | null = null;

    if (this.s3ConfigService.isS3Enabled()) {
      // For S3 uploads, multer-s3 handles the upload automatically
      // The file object will have location and key properties
      const s3File = file as any; // multer-s3 adds extra properties
      filePath = s3File.location; // S3 URL
      filename = s3File.key; // S3 key
      s3Key = s3File.key;
    } else {
      // Local storage
      const fileExtension = path.extname(file.originalname);
      filename = `${Date.now()}-${Math.random().toString(36).substring(2)}${fileExtension}`;
      filePath = path.join(this.uploadDir, filename);

      // Save file to disk
      fs.writeFileSync(filePath, file.buffer);
    }

    let width: number | null = null;
    let height: number | null = null;
    let thumbnailPath: string | null = null;

    // Process images
    if (mediaType === 'image') {
      try {
        const metadata = await sharp(file.buffer).metadata();
        width = metadata.width || null;
        height = metadata.height || null;

        // Create thumbnail for images
        if (this.s3ConfigService.isS3Enabled()) {
          // For S3, we'll store thumbnail info but not create actual thumbnail files for now
          // This can be enhanced later with S3 thumbnail uploads
          thumbnailPath = null;
        } else {
          const thumbnailFilename = `thumb_${filename}`;
          thumbnailPath = path.join(this.thumbnailDir, thumbnailFilename);

          await sharp(file.buffer)
            .resize(300, 300, { fit: 'inside', withoutEnlargement: true })
            .jpeg({ quality: 80 })
            .toFile(thumbnailPath);
        }
      } catch (error) {
        console.error('Error processing image:', error);
      }
    }

    // Create media record
    const media = this.mediaRepository.create({
      original_filename: file.originalname,
      filename,
      mime_type: file.mimetype,
      file_size: file.size,
      media_type: mediaType,
      file_path: filePath,
      uploaded_by: uploadedBy,
      width,
      height,
      duration: null, // TODO: Extract duration for video/audio files
      thumbnail_path: thumbnailPath,
      s3_key: s3Key, // Store S3 key for S3 uploads
    });

    return this.mediaRepository.save(media);
  }

  async getMedia(id: string): Promise<Media> {
    const media = await this.mediaRepository.findOne({ where: { id } });
    if (!media) {
      throw new NotFoundException('Media not found');
    }
    return media;
  }

  async getMediaFile(id: string): Promise<{ media: Media; buffer: Buffer }> {
    const media = await this.getMedia(id);

    if (this.s3ConfigService.isS3Enabled() && media.s3_key) {
      // Get file from S3
      try {
        const command = new GetObjectCommand({
          Bucket: this.s3ConfigService.getBucketName(),
          Key: media.s3_key,
        });

        const response = await this.s3ConfigService.getS3Client().send(command);
        if (!response.Body) {
          throw new NotFoundException('Media file body not found in S3');
        }
        const buffer = Buffer.from(await response.Body.transformToByteArray());
        return { media, buffer };
      } catch (error) {
        console.error('Error fetching file from S3:', error);
        throw new NotFoundException('Media file not found in S3');
      }
    } else {
      // Get file from local storage
      if (!fs.existsSync(media.file_path)) {
        throw new NotFoundException('Media file not found on disk');
      }

      const buffer = fs.readFileSync(media.file_path);
      return { media, buffer };
    }
  }

  async getThumbnail(id: string): Promise<{ media: Media; buffer: Buffer }> {
    const media = await this.getMedia(id);

    if (!media.thumbnail_path || !fs.existsSync(media.thumbnail_path)) {
      throw new NotFoundException('Thumbnail not found');
    }

    const buffer = fs.readFileSync(media.thumbnail_path);
    return { media, buffer };
  }

  async deleteMedia(id: string): Promise<void> {
    const media = await this.getMedia(id);

    if (this.s3ConfigService.isS3Enabled() && media.s3_key) {
      // Delete from S3
      try {
        const command = new DeleteObjectCommand({
          Bucket: this.s3ConfigService.getBucketName(),
          Key: media.s3_key,
        });
        await this.s3ConfigService.getS3Client().send(command);
      } catch (error) {
        console.error('Error deleting file from S3:', error);
      }
    } else {
      // Delete files from local disk
      if (fs.existsSync(media.file_path)) {
        fs.unlinkSync(media.file_path);
      }

      if (media.thumbnail_path && fs.existsSync(media.thumbnail_path)) {
        fs.unlinkSync(media.thumbnail_path);
      }
    }

    // Delete from database
    await this.mediaRepository.delete(id);
  }

  async getMediaByUser(username: string): Promise<Media[]> {
    return this.mediaRepository.find({
      where: { uploaded_by: username },
      order: { uploaded_at: 'DESC' },
    });
  }

  /**
   * Generate a signed URL for accessing a media file
   * @param id Media ID
   * @param expiresIn Expiration time in seconds (default: 1 hour)
   * @returns Object containing media info and signed URL
   */
  async getMediaSignedUrl(id: string, expiresIn: number = 3600): Promise<{
    media: Media;
    signedUrl: string;
    expiresAt: Date;
  }> {
    const media = await this.getMedia(id);
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    if (this.s3ConfigService.isS3Enabled() && media.s3_key) {
      // Generate S3 presigned URL
      const signedUrl = await this.s3ConfigService.generatePresignedGetUrl(
        media.s3_key,
        expiresIn
      );

      return {
        media,
        signedUrl,
        expiresAt,
      };
    } else {
      // For local storage, generate a temporary signed URL using JWT or similar
      // This is a simplified approach - in production you might want to use a more robust solution
      const token = this.generateLocalSignedToken(media, expiresIn);
      const signedUrl = `${process.env.API_BASE_URL || 'http://localhost:3000'}/api/media/${id}/signed?token=${token}`;

      return {
        media,
        signedUrl,
        expiresAt,
      };
    }
  }

  /**
   * Generate a signed URL for uploading media directly to S3
   * @param filename Original filename
   * @param contentType MIME type
   * @param uploadedBy Username of uploader
   * @param expiresIn Expiration time in seconds (default: 1 hour)
   * @returns Object containing upload URL and media info
   */
  async generateUploadSignedUrl(
    filename: string,
    contentType: string,
    uploadedBy: string,
    expiresIn: number = 3600
  ): Promise<{
    uploadUrl: string;
    mediaId: string;
    s3Key: string;
    expiresAt: Date;
  }> {
    if (!this.s3ConfigService.isS3Enabled()) {
      throw new Error('Direct upload to S3 is only available when S3 is enabled');
    }

    // Generate unique S3 key
    const fileExtension = filename.split('.').pop();
    const s3Key = `media/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`;
    
    // Generate presigned PUT URL
    const uploadUrl = await this.s3ConfigService.generatePresignedPutUrl(
      s3Key,
      contentType,
      expiresIn
    );

    // Create media record in database (without file data)
    const media = this.mediaRepository.create({
      original_filename: filename,
      filename: s3Key.split('/').pop() || filename,
      mime_type: contentType,
      file_size: 0, // Will be updated after upload
      media_type: this.getMediaType(contentType),
      file_path: `https://${this.s3ConfigService.getBucketName()}.s3.amazonaws.com/${s3Key}`,
      uploaded_by: uploadedBy,
      s3_key: s3Key,
    });

    const savedMedia = await this.mediaRepository.save(media);
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    return {
      uploadUrl,
      mediaId: savedMedia.id,
      s3Key,
      expiresAt,
    };
  }

  /**
   * Update media record after successful S3 upload
   * @param mediaId Media ID
   * @param fileSize Actual file size
   * @param metadata Additional metadata (width, height, etc.)
   */
  async updateMediaAfterUpload(
    mediaId: string,
    fileSize: number,
    metadata?: {
      width?: number;
      height?: number;
      duration?: number;
    }
  ): Promise<Media> {
    const media = await this.getMedia(mediaId);
    
    media.file_size = fileSize;
    if (metadata) {
      media.width = metadata.width || null;
      media.height = metadata.height || null;
      media.duration = metadata.duration || null;
    }

    return this.mediaRepository.save(media);
  }

  /**
   * Generate a simple signed token for local file access
   * In production, use a proper JWT library
   */
  private generateLocalSignedToken(media: Media, expiresIn: number): string {
    // This is a simplified implementation
    // In production, use a proper JWT library with secret key
    const payload = {
      mediaId: media.id,
      expiresAt: Date.now() + expiresIn * 1000,
    };
    
    // Simple base64 encoding (NOT secure for production)
    return Buffer.from(JSON.stringify(payload)).toString('base64');
  }
}
