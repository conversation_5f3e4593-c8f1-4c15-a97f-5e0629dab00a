import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MediaController } from './media.controller';
import { MediaService } from './media.service';
import { Media } from './media.entity';
import { S3ConfigService } from './s3-config.service';
import * as multerS3 from 'multer-s3';
import { S3Client } from '@aws-sdk/client-s3';

@Module({
  imports: [
    TypeOrmModule.forFeature([Media]),
    MulterModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const useS3 = configService.get('USE_S3_STORAGE') === 'true';

        if (useS3) {
          // S3 Configuration
          const accessKeyId = configService.get('AWS_ACCESS_KEY_ID');
          const secretAccessKey = configService.get('AWS_SECRET_ACCESS_KEY');
          const bucketName = configService.get('AWS_S3_BUCKET');

          if (!accessKeyId || !secretAccessKey || !bucketName) {
            throw new Error('AWS credentials and bucket name are required when USE_S3_STORAGE is true');
          }

          const s3Client = new S3Client({
            region: configService.get('AWS_REGION') || 'us-east-1',
            credentials: {
              accessKeyId,
              secretAccessKey,
            },
          });

          return {
            storage: multerS3({
              s3: s3Client,
              bucket: bucketName,
              key: (req, file, cb) => {
                const fileExtension = file.originalname.split('.').pop();
                const filename = `media/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`;
                cb(null, filename);
              },
              contentType: multerS3.AUTO_CONTENT_TYPE,
            }),
            limits: {
              fileSize: 10 * 1024 * 1024, // 10MB
            },
          };
        } else {
          // Local storage configuration (fallback)
          return {
            limits: {
              fileSize: 10 * 1024 * 1024, // 10MB
            },
          };
        }
      },
    }),
  ],
  controllers: [MediaController],
  providers: [MediaService, S3ConfigService],
  exports: [MediaService, S3ConfigService],
})
export class MediaModule {}
